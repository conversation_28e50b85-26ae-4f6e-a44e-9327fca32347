// Check form submission logic

// Initialize check form functionality
function initializeCheckForm() {
    const checkForm = document.getElementById("checkForm");
    const uploadActiveInput = document.getElementById("main_product_image_upload_active");
    const urlInput = document.getElementById("main_product_image_url");
    const fileInput = document.getElementById("main_product_image_upload");
    
    if (!checkForm) return;

    // Prevent submitting both file and URL for main product image
    checkForm.addEventListener("submit", function(e) {
        if (uploadActiveInput.value === "true" && urlInput.value.trim() !== "") {
            alert("Please either upload a file OR provide a URL, not both.");
            e.preventDefault();
            return;
        }
        if (uploadActiveInput.value === "false") {
            fileInput.value = "";
        }
    });

    // Additional validation for form submission
    checkForm.addEventListener("submit", function(e) {
        const otherUploadActiveInput = document.getElementById("other_product_images_upload_active");
        const otherUrlInput = document.getElementById("other_product_images_url");
        const ipUploadActiveInput = document.getElementById("ip_images_upload_active");
        const ipUrlInput = document.getElementById("ip_images_url");
        const otherFilesInput = document.getElementById("other_product_images_upload");
        const ipFilesInput = document.getElementById("ip_images_upload");

        const otherUploadActive = otherUploadActiveInput.value;
        const otherUrl = otherUrlInput.value.trim();
        if(otherUploadActive === "true" && otherUrl !== "") {
            alert("Please either upload Other Product Images OR provide URLs, not both.");
            e.preventDefault();
            return;
        }
        const ipUploadActive = ipUploadActiveInput.value;
        const ipUrl = ipUrlInput.value.trim();
        if(ipUploadActive === "true" && ipUrl !== "") {
            alert("Please either upload IP Images OR provide URLs, not both.");
            e.preventDefault();
            return;
        }
        if(otherUploadActive === "false") {
            otherFilesInput.value = "";
        }
        if(ipUploadActive === "false") {
            ipFilesInput.value = "";
        }
    });

    // Main submission logic with spinner and temporary message
    checkForm.addEventListener("submit", function(e) {
        e.preventDefault();
        
        // Disable the submit button to prevent duplicate submissions
        const submitButton = this.querySelector("button[type='submit']");
        submitButton.disabled = true;
        
        // Display a spinner and processing message
        const outputDiv = document.getElementById("output");
        outputDiv.innerHTML = `
            <div class="info-message">
                <div style='display:flex; align-items:center; gap:10px;'>
                    <div class='spinner'></div>
                    <span data-i18n='processing'></span>
                </div>
            </div>`;
        
        // Trigger language update immediately after creating the message
        document.querySelector('[data-i18n="processing"]').textContent = i18n[document.getElementById("language").value].processing;

        // Immediate scroll to top with header offset
        const header = document.querySelector('.header');
        const headerHeight = header ? header.offsetHeight : 0;
        
        // New approach: Use documentElement scroll with CSS scroll-padding
        document.documentElement.style.scrollPaddingTop = `${headerHeight}px`;
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });

        // Gather form values
        const formData = gatherFormData();

        // Add timeout controller
        const controller = new AbortController();
        const timeoutDuration = 600000; // 10 minutes
        const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

        // Make the API call
        const api_endpoint = "http://localhost:5000/check_api";
        // const api_endpoint = "https://api.maidalv.com/check_api";
        
        submitCheckRequest(api_endpoint, formData, controller, timeoutId, submitButton, outputDiv);
    });
}

// Gather form data
function gatherFormData() {
    const uploadActiveInput = document.getElementById("main_product_image_upload_active");
    const previewImage = document.getElementById("main_product_image_preview");
    const urlInput = document.getElementById("main_product_image_url");
    
    const api_key = document.getElementById("api_key").value;
    const mainProductImageValue = (uploadActiveInput.value === "true") ? previewImage.src : urlInput.value.trim();
    
    // For other product images
    let other_product_images;
    if (document.getElementById("other_product_images_upload_active").value === "true") {
        other_product_images = window.otherProductDataUrls;
    } else {
        other_product_images = document.getElementById("other_product_images_url").value.split(",").map(item => item.trim()).filter(item => item);
    }
    
    // For IP images, use the data URLs if uploaded
    const ipUploadActiveInput = document.getElementById("ip_images_upload_active");
    const ip_images = (ipUploadActiveInput.value === "true") 
                        ? window.ipDataUrls 
                        : document.getElementById("ip_images_url").value.split(",").map(item => item.trim()).filter(item => item);
    
    const ip_keywords = document.getElementById("ip_keywords").value.split(",").map(item => item.trim()).filter(item => item);
    const description = document.getElementById("description").value;
    const reference_text = document.getElementById("reference_text").value;
    
    // For Reference images, use the data URLs if uploaded
    const reference_images = (document.getElementById("reference_images_upload_active").value === "true") 
                            ? window.referenceDataUrls 
                            : document.getElementById("reference_images").value.split(",").map(item => item.trim()).filter(item => item);
    
    return {
        api_key: api_key,
        main_product_image: mainProductImageValue,
        other_product_images: other_product_images,
        ip_images: ip_images,
        ip_keywords: ip_keywords,
        description: description,
        reference_text: reference_text,
        reference_images: reference_images,
        language: document.getElementById("language").value
    };
}

// Submit check request
function submitCheckRequest(api_endpoint, data, controller, timeoutId, submitButton, outputDiv) {
    fetch(api_endpoint, {
        method: "POST",
        headers: {"Content-Type": "application/json"},
        body: JSON.stringify(data),
        signal: controller.signal
    })
    .then(response => {
        clearTimeout(timeoutId);
        return handleResponse(response);
    })
    .then(initialResponse => {
        if (!initialResponse.check_id) throw new Error('Missing check ID in response');
        
        // If the response indicates an error, handle it
        if (initialResponse.status === 'failed' || initialResponse.error) {
            const errorMessage = initialResponse.error || 'An unknown error occurred';
            throw new Error(errorMessage);
        }
        
        // Display initial queue position and estimated time if available
        if (initialResponse.status === 'processing') {                        
            let estimatedTimeMessage = initialResponse.estimated_completion_time !== undefined
                ? `${i18n[document.getElementById("language").value].estimated_time}: ${initialResponse.estimated_completion_time}`
                : '';

            outputDiv.innerHTML = `
                <div class="info-message">
                    <div style='display:flex; align-items:center; gap:10px;'>
                        <div class='spinner'></div>
                        <span data-i18n='running'></span>
                        <span>${estimatedTimeMessage}</span>
                    </div>
                </div>`;
            document.querySelector('[data-i18n="running"]').textContent = i18n[document.getElementById("language").value].running;
        }
        
        // Start polling for results
        startPolling(initialResponse.check_id, outputDiv, submitButton);
    })
    .catch(error => {
        clearTimeout(timeoutId);
        handleError(error, outputDiv, submitButton);
    });
}

// Handle response
function handleResponse(response) {
    // Add explicit error handling based on HTTP status codes
    if (!response.ok) {
        const language = document.getElementById("language").value;
        const errorMessages = {
            401: i18n[language].error_invalid_api_key,
            429: i18n[language].error_rate_limit,
            500: i18n[language].error_server
        };
        
        // Get appropriate error message or use a generic one
        const errorMessage = errorMessages[response.status] || 
                           `Error ${response.status}: ${response.statusText}`;
        
        // Try to get more details from the response JSON
        return response.json().then(errorJson => {
            throw new Error(`${errorMessage} - ${errorJson.message || errorJson.error || ''} ${errorJson.error_code ? `(${errorJson.error_code})` : ''}`);
        }).catch(e => {
            // If we couldn't parse JSON, just throw the original error
            if (e.message && e.message.includes('JSON')) {
                throw new Error(errorMessage);
            }
            throw e; // Otherwise, rethrow the error with the additional details
        });
    }
    if (!response.headers.get("content-type").includes("application/json")) {
        throw new Error(`Invalid content type: ${response.headers.get("content-type")}`);
    }
    return response.json();
}

// Start polling for results
function startPolling(checkId, outputDiv, submitButton) {
    const pollInterval = 5000; // 5 seconds
    const maxPolls = 60; // 5 minutes total
    let polls = 0;

    const poller = setInterval(() => {
        fetch(`/check_status/${checkId}`)
        .then(response => {
            // Add explicit error handling for status endpoint too
            if (!response.ok) {
                const language = document.getElementById("language").value;
                const errorMessages = {
                    401: i18n[language].error_invalid_api_key,
                    429: i18n[language].error_rate_limit,
                    500: i18n[language].error_server,
                    404: response.statusText // Use status text for not found errors
                };

                const errorMessage = errorMessages[response.status] ||
                                    `Error ${response.status}: ${response.statusText}`;

                // If the fetch returned an error, we'll still try to extract the JSON
                // to get more details from the API
                return response.json().then(errorJson => {
                    throw new Error(`${errorMessage} - ${errorJson.message || errorJson.error || ''}`);
                }).catch(e => {
                    // If we couldn't get JSON, just throw the original error
                    throw new Error(errorMessage);
                });
            }
            return response.json();
        })
        .then(statusResponse => {
            console.log("Full JSON response:", statusResponse);

            if (statusResponse.status === 'error') {
                // Handle error status in a successful response
                clearInterval(poller);
                const language = document.getElementById("language").value;
                const errorMessage = statusResponse.message || i18n[language].error_server;
                const errorCode = statusResponse.error_code || '';

                outputDiv.innerHTML = `
                    <div class="error-message">
                        <strong>Error</strong>
                        <p>${errorMessage}</p>
                        ${errorCode ? `<div class="error-code">${errorCode}</div>` : ''}
                    </div>
                `;
                submitButton.disabled = false;
                return;
            }

            if (statusResponse.status === 'completed') {
                clearInterval(poller);
                // Process results as before
                displayResults(statusResponse.result);
                submitButton.disabled = false;
            } else if (statusResponse.status === 'processing') {
                // Update queue position and estimated time
                let estimatedTimeMessage = statusResponse.estimated_completion_time !== undefined
                    ? `${i18n[document.getElementById("language").value].estimated_time} ${statusResponse.estimated_completion_time}`
                    : '';

                outputDiv.innerHTML = `
                    <div class="info-message">
                        <div style='display:flex; align-items:center; gap:10px;'>
                            <div class='spinner'></div>
                            <span data-i18n='processing'></span>
                            <span>${estimatedTimeMessage}</span>
                        </div>
                    </div>`;
                document.querySelector('[data-i18n="processing"]').textContent = i18n[document.getElementById("language").value].processing;
                // Update the dynamic text (queue position and estimated time)
                document.querySelectorAll('[data-i18n]').forEach(elem => {
                    const key = elem.getAttribute('data-i18n');
                    if (i18n[document.getElementById("language").value][key]) {
                        elem.textContent = i18n[document.getElementById("language").value][key];
                    }
                });
            }
            if (polls++ >= maxPolls) {
                clearInterval(poller);
                throw new Error('Analysis timed out');
            }
        })
        .catch(error => {
            // Clear interval if there's an error to prevent continued polling
            clearInterval(poller);
            handleError(error, outputDiv, submitButton);
        });
    }, pollInterval);
}

// Handle errors
function handleError(error, outputDiv, submitButton) {
    const language = document.getElementById("language").value;

    let errorMessage = error.message;
    let errorCode = '';

    // Specialized error messages
    if (error.name === 'AbortError') {
        errorMessage = i18n[language].error_timeout;
        errorCode = 'TIMEOUT';
    } else if (error.message.includes('NetworkError')) {
        errorMessage = i18n[language].error_network;
        errorCode = 'NETWORK_ERROR';
    } else if (error.message.includes('Missing required field')) {
        // Extract the specific field name from the error message
        const fieldMatch = error.message.match(/Missing required field: (.+)( - .*)?$/);
        const missingField = fieldMatch && fieldMatch[1] ? fieldMatch[1] : '';
        errorMessage = `${i18n[language].error_missing_field}: ${missingField}`;
        errorCode = 'MISSING_FIELD';
    }

    // Extract error code if it's in the message
    const codeMatch = errorMessage.match(/\(([A-Z_]+)\)$/);
    if (codeMatch && codeMatch[1]) {
        errorCode = codeMatch[1];
        // Remove the code from the displayed message
        errorMessage = errorMessage.replace(/\s*\([A-Z_]+\)$/, '');
    }

    // Display the error with better formatting using our new CSS classes
    outputDiv.innerHTML = `
        <div class="error-message">
            <strong>Error</strong>
            <p>${errorMessage}</p>
            ${errorCode ? `<div class="error-code">${errorCode}</div>` : ''}
        </div>
    `;

    // Re-enable submit button even on error
    submitButton.disabled = false;
}

// Export functions for global access
window.initializeCheckForm = initializeCheckForm;
