// Image upload and preview functionality

// Global arrays to store data URLs for different image types
let otherProductDataUrls = [];
let ipDataUrls = [];
let referenceDataUrls = [];

// Main Product Image functionality
function initializeMainProductImage() {
    const fileInput = document.getElementById("main_product_image_upload");
    const urlInput = document.getElementById("main_product_image_url");
    const previewImage = document.getElementById("main_product_image_preview");
    const cancelButton = document.getElementById("cancel_preview");
    const uploadActiveInput = document.getElementById("main_product_image_upload_active");
    
    if (fileInput) {
        fileInput.addEventListener("change", function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    previewImage.src = event.target.result;
                    previewImage.style.display = "block";
                    cancelButton.style.display = "inline";
                    uploadActiveInput.value = "true";
                    urlInput.value = "";
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    if (cancelButton) {
        cancelButton.addEventListener("click", function() {
            fileInput.value = "";
            previewImage.style.display = "none";
            cancelButton.style.display = "none";
            uploadActiveInput.value = "false";
        });
    }
}

// Other Product Images functionality
function initializeOtherProductImages() {
    const otherFilesInput = document.getElementById("other_product_images_upload");
    const otherUrlInput = document.getElementById("other_product_images_url");
    const otherPreviewsDiv = document.getElementById("other_product_images_previews");
    const cancelOtherButton = document.getElementById("cancel_other_product_images");
    const otherUploadActiveInput = document.getElementById("other_product_images_upload_active");
    
    if (otherFilesInput) {
        otherFilesInput.addEventListener("change", function(e) {
            otherProductDataUrls = []; // reset the array on change
            otherPreviewsDiv.innerHTML = "";
            const files = e.target.files;
            if(files.length > 0) {
                otherUploadActiveInput.value = "true";
                otherUrlInput.value = "";
                cancelOtherButton.style.display = "inline";
                for(let i = 0; i < files.length; i++){
                    (function(file){
                        const reader = new FileReader();
                        reader.onload = function(event) {
                            const result = event.target.result;
                            otherProductDataUrls.push(result); // store the data URL
                            const img = document.createElement("img");
                            img.src = result;
                            img.style.maxWidth = "100px";
                            img.style.maxHeight = "100px";
                            img.style.marginRight = "5px";
                            img.style.marginBottom = "5px";
                            otherPreviewsDiv.appendChild(img);
                        };
                        reader.readAsDataURL(file);
                    })(files[i]);
                }
            }
        });
    }
    
    if (cancelOtherButton) {
        cancelOtherButton.addEventListener("click", function() {
            otherFilesInput.value = "";
            otherPreviewsDiv.innerHTML = "";
            otherUploadActiveInput.value = "false";
            cancelOtherButton.style.display = "none";
        });
    }
}

// IP Images functionality
function initializeIpImages() {
    const ipFilesInput = document.getElementById("ip_images_upload");
    const ipUrlInput = document.getElementById("ip_images_url");
    const ipPreviewsDiv = document.getElementById("ip_images_previews");
    const cancelIpButton = document.getElementById("cancel_ip_images");
    const ipUploadActiveInput = document.getElementById("ip_images_upload_active");

    if (ipFilesInput) {
        ipFilesInput.addEventListener("change", function(e) {
            ipDataUrls = []; // reset the array on change
            ipPreviewsDiv.innerHTML = "";
            const files = e.target.files;
            if(files.length > 0){
                ipUploadActiveInput.value = "true";
                ipUrlInput.value = "";
                cancelIpButton.style.display = "inline";
                for(let i = 0; i < files.length; i++){
                    (function(file){
                        const reader = new FileReader();
                        reader.onload = function(event) {
                            const result = event.target.result;
                            ipDataUrls.push(result);  // collect each data URL
                            const img = document.createElement("img");
                            img.src = result;
                            img.style.maxWidth = "100px";
                            img.style.maxHeight = "100px";
                            img.style.marginRight = "5px";
                            img.style.marginBottom = "5px";
                            ipPreviewsDiv.appendChild(img);
                        };
                        reader.readAsDataURL(file);
                    })(files[i]);
                }
            }
        });
    }

    if (cancelIpButton) {
        cancelIpButton.addEventListener("click", function() {
            ipFilesInput.value = "";
            ipPreviewsDiv.innerHTML = "";
            ipUploadActiveInput.value = "false";
            cancelIpButton.style.display = "none";
        });
    }
}

// Reference Images functionality
function initializeReferenceImages() {
    const referenceFilesInput = document.getElementById("reference_images_upload");
    const referencePreviewsDiv = document.getElementById("reference_images_previews");
    const cancelReferenceButton = document.getElementById("cancel_reference_images");
    const referenceUploadActiveInput = document.getElementById("reference_images_upload_active");

    if (referenceFilesInput) {
        referenceFilesInput.addEventListener("change", function(e) {
            referenceDataUrls = []; // Reset the array on new selection
            referencePreviewsDiv.innerHTML = "";
            const files = e.target.files;
            if (files.length > 0) {
                referenceUploadActiveInput.value = "true";
                // Loop through selected files and show previews
                for (let i = 0; i < files.length; i++) {
                    (function(file) {
                        const reader = new FileReader();
                        reader.onload = function(event) {
                            const result = event.target.result;
                            referenceDataUrls.push(result); // Store the data URL
                            const img = document.createElement("img");
                            img.src = result;
                            img.style.maxWidth = "150px";
                            img.style.marginRight = "10px";
                            img.style.marginTop = "10px";
                            referencePreviewsDiv.appendChild(img);
                        };
                        reader.readAsDataURL(file);
                    })(files[i]);
                }
                cancelReferenceButton.style.display = "inline";
            }
        });
    }

    if (cancelReferenceButton) {
        cancelReferenceButton.addEventListener("click", function() {
            referenceFilesInput.value = "";
            referencePreviewsDiv.innerHTML = "";
            referenceUploadActiveInput.value = "false";
            cancelReferenceButton.style.display = "none";
        });
    }
}

// Initialize all image upload functionality
function initializeImageUploads() {
    initializeMainProductImage();
    initializeOtherProductImages();
    initializeIpImages();
    initializeReferenceImages();
}

// Export functions and variables for global access
window.otherProductDataUrls = otherProductDataUrls;
window.ipDataUrls = ipDataUrls;
window.referenceDataUrls = referenceDataUrls;
window.initializeImageUploads = initializeImageUploads;
