Client files: 
- App: 
   - Starts on https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/tro-1330776830/2025/01/06/313dff300cbe725038eaaa7788413ed66ea9139385690b40bcf0961743c8bc9a.jpg
   - Get downloaded into a tmp folder (in Do_Check_download.py)
   - Get uploaded to cos (http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/query/filename.jpg) (in Do_Check_Trademark.py)
      - This does not make sense, we should copy on COS from original location to /checks/{check_id}/query/filename.jpg  (in Do_Check_Download.py)
- API: 
   - Starts on Client provided URL or Base 64 image
   - Get downloaded/saved into a tmp folder (in Do_Check_download.py)
   - Get uploaded to cos (http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/query/filename.jpg) (in Do_Check_Trademark.py). There is a big cost to this. Alternatrive include:
      - OneDrive
      - Hetzner cold storage

Client part files for copyright: 
- Parts are created by Do_Check_Copyright.py
- All picture selected by RAG gets uploaded to cos (/checks/{check_id}/query/filename.jpg) (in Do_Check_Copyright.py).  However:
    - If a check is positive, we should upload to COS for frontend or providing results back to API Client (or a cheaper location)
    - If check is negative, then it is only for langfuse. We should put on the files on local hdd (i.e. the server, because that is the fastest), or onedrive because that is the cheapest
We should also show the original image with the blue quare on it as part of the output of the bounding box function

Client part files for trademark: are not saved 

IP files:
- Currently we download them from COS. !!! They should all be local for speed and bandwidth. It is only 3000 copyright files and 1000 patent files.
- Then if it is a match (?) we copy onCOS from one folder to another



============================================================================
Changes: 
Client: 
- upload to new COS, but resized to 512 and webp compressed (is Java already doing this?)
- delete from 2025/01/06.... as no longer required (is Java doing this: they are all jpg)
IP files: same logic except the original is in https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/IP/Trademaks/xxxxyyyy.webp (where xxxyyy use sha256 to encore the name)


Transition:
1. Put all IP on tro-1330776830
2. Change check code to put all the /checks/{check_id}/ files on both COS
3. Copy existing checks/{check_id}/ to tro-1330776830
4. Change Java / Mini app to take from tro-1330776830
5. Remove troimages from Check code



Temp dir: 
temp_dir/
  ├── product_images/  ???
  ├── ip_images/  ????
  ├── reference_images/  ????
  └── copyright/ (for processed copyright parts)
  └── product_images/trademark (for processed trademark parts)

COS:
Query images: checks/{check_id}/query/{filename}   - For product_images, ip_images, reference_images and copyright parts (not trademark parts)
IP images: ip_assets/{ip_type}s/{obfuscated_reg_no}.webp