<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Reverse Check Functions</title>
    <script src="static/api_studio.js"></script>
</head>
<body>
    <h1>Test Reverse Check Functions</h1>
    
    <!-- Test elements -->
    <input type="text" id="reverse_check_api_key" placeholder="API Key">
    <input type="date" id="start_date">
    <input type="date" id="end_date">
    <button id="fetchReverseCheckBtn">Fetch Reverse Check</button>
    <button id="toggleReportBtn">Toggle Report</button>
    
    <div id="reverseCheckTree"></div>
    <div id="output"></div>
    
    <!-- Language selector -->
    <select id="languageSelect">
        <option value="en">English</option>
        <option value="zh">中文</option>
    </select>
    <input type="hidden" id="language" value="en">
    
    <script>
        // Test if functions are accessible
        console.log('Testing reverse check functions...');
        
        // Test if functions exist
        console.log('initializeReverseCheckPage exists:', typeof initializeReverseCheckPage === 'function');
        console.log('fetchReverseCheckResults exists:', typeof fetchReverseCheckResults === 'function');
        console.log('displayReverseCheckList exists:', typeof displayReverseCheckList === 'function');
        console.log('toggleDateGroup exists:', typeof toggleDateGroup === 'function');
        console.log('displayReverseCheckResult exists:', typeof displayReverseCheckResult === 'function');
        console.log('displayReverseCheckResultDetails exists:', typeof displayReverseCheckResultDetails === 'function');
        console.log('getCookie exists:', typeof getCookie === 'function');
        console.log('setCookie exists:', typeof setCookie === 'function');
        
        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing reverse check page...');
            try {
                initializeReverseCheckPage();
                console.log('✅ initializeReverseCheckPage called successfully');
            } catch (error) {
                console.error('❌ Error calling initializeReverseCheckPage:', error);
            }
        });
        
        // Test button click
        document.addEventListener('DOMContentLoaded', function() {
            const btn = document.getElementById('fetchReverseCheckBtn');
            btn.addEventListener('click', function() {
                console.log('✅ Button click event fired');
            });
        });
    </script>
</body>
</html>
