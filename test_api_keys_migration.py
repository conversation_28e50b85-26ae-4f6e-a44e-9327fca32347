#!/usr/bin/env python3
"""
Test script to validate API keys migration from JSON to database.
"""

import sys
import os
import json

# Add the Qdrant API path to sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Qdrant', 'api'))

from utils.db import (
    create_api_keys_table, 
    get_api_keys_as_dict, 
    get_cached_api_keys,
    refresh_api_keys_cache,
    _is_cache_valid
)

def test_json_vs_database():
    """Test that database contains the same data as JSON file"""
    print("🔍 Testing JSON vs Database consistency...")
    
    # Load JSON data
    try:
        with open('api_keys.json', 'r') as f:
            json_data = json.load(f)
        print(f"📄 Loaded {len(json_data)} API keys from JSON")
    except Exception as e:
        print(f"❌ Failed to load JSON: {str(e)}")
        return False
    
    # Load database data
    try:
        db_data = get_api_keys_as_dict()
        print(f"🗄️ Loaded {len(db_data)} API keys from database")
    except Exception as e:
        print(f"❌ Failed to load from database: {str(e)}")
        return False
    
    # Compare counts
    if len(json_data) != len(db_data):
        print(f"❌ Count mismatch: JSON has {len(json_data)}, DB has {len(db_data)}")
        return False
    
    # Compare each key
    mismatches = []
    for api_key, json_config in json_data.items():
        if api_key not in db_data:
            mismatches.append(f"API key {api_key} missing from database")
            continue
            
        db_config = db_data[api_key]
        
        # Compare each field
        for field in ['id', 'client_name', 'rate_limit', 'daily_limit']:
            if json_config.get(field) != db_config.get(field):
                mismatches.append(f"API key {api_key}: {field} mismatch - JSON: {json_config.get(field)}, DB: {db_config.get(field)}")
    
    if mismatches:
        print("❌ Data mismatches found:")
        for mismatch in mismatches[:5]:  # Show first 5 mismatches
            print(f"  - {mismatch}")
        if len(mismatches) > 5:
            print(f"  ... and {len(mismatches) - 5} more")
        return False
    
    print("✅ JSON and database data match perfectly")
    return True


def test_caching():
    """Test the caching mechanism"""
    print("🔍 Testing caching mechanism...")
    
    try:
        # First call should fetch from database
        print("📥 First call (should fetch from DB)...")
        data1 = get_cached_api_keys()
        
        # Second call should use cache
        print("📋 Second call (should use cache)...")
        data2 = get_cached_api_keys()
        
        # Check if data is the same
        if data1 == data2:
            print("✅ Cache consistency test passed")
        else:
            print("❌ Cache consistency test failed")
            return False
            
        # Test cache validation
        print("🕐 Testing cache validation...")
        is_valid = _is_cache_valid()
        print(f"Cache valid: {is_valid}")
        
        # Test refresh
        print("🔄 Testing cache refresh...")
        refreshed_data = refresh_api_keys_cache()
        
        if len(refreshed_data) == len(data1):
            print("✅ Cache refresh test passed")
        else:
            print("❌ Cache refresh test failed")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Caching test failed: {str(e)}")
        return False


def test_sample_api_keys():
    """Test a few sample API keys"""
    print("🔍 Testing sample API keys...")
    
    try:
        api_keys = get_cached_api_keys()
        
        # Test a few known keys from the JSON
        test_keys = [
            "37457-48774-8887-1882",  # TestSerge
            "65298-12345-9876-5432",  # Bluelansi
            "88888-74847-1974-7164"   # MiniApp
        ]
        
        for test_key in test_keys:
            if test_key in api_keys:
                config = api_keys[test_key]
                print(f"✅ {test_key}: {config['client_name']} (ID: {config['id']})")
            else:
                print(f"❌ {test_key}: Not found in database")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Sample API keys test failed: {str(e)}")
        return False


def main():
    """Main test function"""
    print("🧪 Starting API Keys Migration Tests")
    print("=" * 50)
    
    tests = [
        ("JSON vs Database Consistency", test_json_vs_database),
        ("Caching Mechanism", test_caching),
        ("Sample API Keys", test_sample_api_keys)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Migration is ready for production.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
