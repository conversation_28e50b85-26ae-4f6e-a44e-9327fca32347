import os
from FileManagement.Tencent_COS import async_upload_file_with_retry
from AI.GCV_GetImageParts import get_image_parts_async
from langfuse import observe
import asyncio
import time
import shutil
from Common import Constants
from Check.RAG.qdrant_search import find_similar_assets_qdrant
from Check.Create_Report import create_check_report, create_product_url


@observe()
async def check_copyrights(client, bucket, temp_dir, client_id, check_id, local_product_images, local_client_ip_images, local_reference_images, cases_df, plaintiff_df):

    # 1. Bounding Boxes: Get parts of product pictures
    # We make a copy just for copyright because trademark will also create parts and it should not overwrite the trademark parts
    start_time = time.time()

    for product_image in local_product_images:
        product_image_new = os.path.join(os.path.dirname(product_image), "copyright", os.path.basename(product_image))
        os.makedirs(os.path.dirname(product_image_new), exist_ok=True)
        shutil.copy(product_image, product_image_new)
    print(f"\033[33mCopyright: Copied product images in {time.time() - start_time:.1f} seconds\033[0m")

    start_time = time.time()
    parts_tasks = []
    for product_image in local_product_images:
        product_url = create_product_url(check_id, product_image)
        image_url = ["", product_url.replace(" ", "%20").replace("http", "https")]
        parts_tasks.append(get_image_parts_async("Individual images that might be copyrighted", product_image, image_url=image_url, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX))

    parts_results = await asyncio.gather(*parts_tasks)
    images_parts = [part for sublist in parts_results for part in sublist]
    local_images_parts_paths = [part["path"] for part in images_parts]

    # Exclude images that are less than 6000 pixels or where the max(height/width, width/height) > 2.2
    filtered_image_parts = []
    for part in images_parts:
        try:
            width = int(part["width"])
            height = int(part["height"])
            if width * height >= 6000 and max(width / height, height / width) <= 2.2:
                filtered_image_parts.append(part)
        except (ValueError, KeyError) as e:
            print(f"Error processing image part: {part}. Error: {e}")
            # Decide how to handle errors.  Maybe skip this part, or log it.
            continue  # Skip to the next part

    local_images_parts_paths = [part["path"] for part in filtered_image_parts]
    print(f"\033[33mCopyright: Got parts of product images in {time.time() - start_time:.1f} seconds\033[0m")

    # 2. Check copyright infringement on TRO copyrights
    start_time = time.time()

    # Use Qdrant for vector search
    sim_results = await find_similar_assets_qdrant(
        query_image_paths=local_images_parts_paths + local_client_ip_images + local_reference_images + local_product_images,
        check_id=check_id,
        client_id=client_id,
        ip_type="Copyright",
        temp_dir=temp_dir,
        cases_df=cases_df,
        plaintiff_df=plaintiff_df,
        plaintiff_id=None,
        top_n=3,
        similarity_threshold=0.7
    )

    print(f"\033[33mCopyright: Copyright RAG done for {len(local_client_ip_images+local_reference_images+local_product_images)} pictures and {len(local_images_parts_paths)} parts in {time.time() - start_time:.1f} seconds\033[0m")

    if sim_results:
        start_time = time.time()

        # Concurrently process each copyright check
        copyright_check_tasks = [create_check_report(
            ip_type="Copyright",
            check_id=check_id,
            result=result,
            client=client,
            bucket=bucket,
            model_name="gemini-2.0-flash-exp"
        ) for result in sim_results]
        
        # Upload to COS the images parts (the full image is already uploaded in "check" function) used for these results. We start the upload now, we wait for the result after the "check_one_copyright" are done
        product_part_paths = set([result["product_local_path"] for result in sim_results if "part" in result["product_local_path"]])
        copyright_product_image_upload_task = [asyncio.create_task(
            async_upload_file_with_retry(client=client, bucket=bucket, key=f"checks/{check_id}/query/{os.path.basename(product_part_path)}", file_path=product_part_path)
        ) for product_part_path in product_part_paths]

        copyright_results = await asyncio.gather(*copyright_check_tasks)
        await asyncio.gather(*copyright_product_image_upload_task)

        # Filter out None results (where is_copyright["final_answer"] was not "yes")
        filtered_copyright_results = [r for r in copyright_results if r]

    else:
        filtered_copyright_results = []

    print(f"\033[33m ✅ Copyright: Copyright Report Analysis DONE, for {len(sim_results)} RAG results in {time.time() - start_time:.1f} seconds\033[0m")
    return filtered_copyright_results # not used, but tracked by langfuse
